// Test only the working filters
const API_BASE_URL = 'http://localhost:3000';

async function testWorkingFilters() {
  console.log('🧪 Testing working filters...\n');
  
  const workingTests = [
    {
      name: 'Basic pagination',
      params: { limit: 5, page: 1 }
    },
    {
      name: 'Rating filters',
      params: { rating_min: 3, rating_max: 5, limit: 5 }
    },
    {
      name: 'Review count filters',
      params: { review_count_min: 0, review_count_max: 100, limit: 5 }
    },
    {
      name: 'Status filter',
      params: { status: 'ACTIVE', limit: 5 }
    },
    {
      name: 'Affiliate filters',
      params: { affiliate_status: 'APPROVED', has_affiliate_link: true, limit: 5 }
    },
    {
      name: 'Free tier filter',
      params: { hasFreeTier: true, limit: 5 }
    },
    {
      name: 'API access filter',
      params: { apiAccess: true, limit: 5 }
    },
    {
      name: 'Pricing models',
      params: { pricingModels: ['FREE', 'FREEMIUM'], limit: 5 }
    },
    {
      name: 'Price ranges',
      params: { priceRanges: ['FREE', 'LOW'], limit: 5 }
    },
    {
      name: 'Employee count ranges',
      params: { employeeCountRanges: ['C1_10', 'C11_50'], limit: 5 }
    },
    {
      name: 'Funding stages',
      params: { fundingStages: ['SEED', 'SERIES_A'], limit: 5 }
    },
    {
      name: 'Date filters',
      params: { createdAtFrom: '2023-01-01', createdAtTo: '2024-12-31', limit: 5 }
    },
    {
      name: 'Sorting by rating',
      params: { sortBy: 'averageRating', sortOrder: 'desc', limit: 5 }
    },
    {
      name: 'Sorting by review count',
      params: { sortBy: 'reviewCount', sortOrder: 'desc', limit: 5 }
    },
    {
      name: 'Combined working filters',
      params: { 
        status: 'ACTIVE', 
        hasFreeTier: true, 
        rating_min: 3,
        sortBy: 'averageRating',
        sortOrder: 'desc',
        limit: 5 
      }
    }
  ];
  
  let successCount = 0;
  let failCount = 0;
  
  for (const test of workingTests) {
    try {
      const queryString = new URLSearchParams();
      
      Object.entries(test.params).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => queryString.append(key, v));
        } else if (value !== null && value !== undefined) {
          queryString.append(key, value.toString());
        }
      });
      
      const url = `${API_BASE_URL}/entities?${queryString.toString()}`;
      console.log(`🧪 Testing: ${test.name}`);
      console.log(`📡 URL: ${url.substring(0, 80)}${url.length > 80 ? '...' : ''}`);
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ Success: ${data.data?.length || 0} results, ${data.meta?.totalItems || 0} total`);
        successCount++;
      } else {
        console.log(`❌ Error: ${response.status} - ${data.message}`);
        failCount++;
      }
      
      console.log(''); // Empty line for readability
      
    } catch (error) {
      console.log(`💥 Request failed: ${error.message}\n`);
      failCount++;
    }
  }
  
  console.log('=' * 50);
  console.log(`📊 RESULTS: ${successCount} passed, ${failCount} failed`);
  console.log(`📈 Success rate: ${((successCount / (successCount + failCount)) * 100).toFixed(1)}%`);
  
  return { successCount, failCount };
}

// Test frontend URLs
function testFrontendWorkingFilters() {
  console.log('\n🌐 Frontend working filter URLs:');
  
  const urls = [
    'http://localhost:3001/browse',
    'http://localhost:3001/browse?rating_min=3&rating_max=5',
    'http://localhost:3001/browse?status=ACTIVE&hasFreeTier=true',
    'http://localhost:3001/browse?sortBy=averageRating&sortOrder=desc',
    'http://localhost:3001/browse?affiliate_status=APPROVED&has_affiliate_link=true',
    'http://localhost:3001/browse?pricingModels=FREE&pricingModels=FREEMIUM'
  ];
  
  urls.forEach((url, index) => {
    console.log(`${index + 1}. ${url}`);
  });
}

testWorkingFilters()
  .then((results) => {
    testFrontendWorkingFilters();
    
    if (results.successCount > results.failCount) {
      console.log('\n🎉 Most filters are working! Ready for frontend testing.');
    } else {
      console.log('\n⚠️  Many filters are failing. Backend issues need to be resolved.');
    }
  })
  .catch(console.error);
