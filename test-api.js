/**
 * Quick API contract test
 * Run with: node test-api.js
 * 
 * This will help us understand what fields the backend actually accepts
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

// You'll need to get this from your browser's developer tools when logged in
const AUTH_TOKEN = 'your-auth-token-here';

// Real entity type ID from the API
const AI_TOOL_TYPE_ID = 'fd181400-c9e6-431c-a8bd-c068d0491aba';

const testCases = [
  {
    name: 'Minimal payload',
    payload: {
      name: 'Test Tool Minimal',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: AI_TOOL_TYPE_ID
    }
  },
  {
    name: 'With empty tool_details',
    payload: {
      name: 'Test Tool Empty Details',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: AI_TOOL_TYPE_ID,
      tool_details: {}
    }
  },
  {
    name: 'With basic tool_details',
    payload: {
      name: 'Test Tool Basic',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: AI_TOOL_TYPE_ID,
      tool_details: {
        pricing_details: 'Free tier available'
      }
    }
  },
  {
    name: 'With arrays only',
    payload: {
      name: 'Test Tool Arrays',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: AI_TOOL_TYPE_ID,
      tool_details: {
        key_features: ['Feature 1', 'Feature 2'],
        use_cases: ['Use case 1']
      }
    }
  }
];

async function testAPI() {
  console.log('🧪 Testing API Contract\n');
  
  if (AUTH_TOKEN === 'your-auth-token-here') {
    console.log('❌ Please update AUTH_TOKEN in this file with a real token');
    console.log('   Get it from browser dev tools when logged in to the app');
    return;
  }

  for (const testCase of testCases) {
    console.log(`🔍 ${testCase.name}`);
    console.log(`📤 Payload:`, JSON.stringify(testCase.payload, null, 2));
    
    try {
      const response = await fetch(`${API_BASE_URL}/entities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AUTH_TOKEN}`,
        },
        body: JSON.stringify(testCase.payload),
      });

      const responseText = await response.text();
      
      if (response.ok) {
        console.log(`✅ Success (${response.status})`);
        try {
          const json = JSON.parse(responseText);
          console.log('Response:', json);
        } catch {
          console.log('Response:', responseText);
        }
      } else {
        console.log(`❌ Error (${response.status}):`, responseText);
      }
    } catch (error) {
      console.log(`❌ Network Error:`, error.message);
    }
    
    console.log('---\n');
  }
}

testAPI().catch(console.error);
