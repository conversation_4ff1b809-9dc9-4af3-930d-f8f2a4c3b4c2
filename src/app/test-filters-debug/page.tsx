'use client';

import React, { useState } from 'react';

// Test each component individually
import RatingFilter from '@/components/browse/RatingFilter';
import ReviewCountFilter from '@/components/browse/ReviewCountFilter';
import AffiliateFilter from '@/components/browse/AffiliateFilter';
import PlatformIntegrationFilter from '@/components/browse/PlatformIntegrationFilter';
import StatusQualityFilter from '@/components/browse/StatusQualityFilter';
import EntitySpecificFilters from '@/components/browse/EntitySpecificFilters';

export default function TestFiltersDebugPage() {
  // Test state for all filters
  const [ratingMin, setRatingMin] = useState<number | undefined>();
  const [ratingMax, setRatingMax] = useState<number | undefined>();
  const [reviewCountMin, setReviewCountMin] = useState<number | undefined>();
  const [reviewCountMax, setReviewCountMax] = useState<number | undefined>();
  const [affiliateStatus, setAffiliateStatus] = useState<'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED' | undefined>();
  const [hasAffiliateLink, setHasAffiliateLink] = useState<boolean | undefined>();
  const [integrations, setIntegrations] = useState<string[]>([]);
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [targetAudience, setTargetAudience] = useState<string[]>([]);
  const [status, setStatus] = useState<'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION' | undefined>();
  const [entityTypeFilters, setEntityTypeFilters] = useState<Record<string, any>>({});

  // Mock entity types for testing
  const mockEntityTypes = [
    { id: 'course-id', name: 'Course' },
    { id: 'job-id', name: 'Job' },
    { id: 'hardware-id', name: 'Hardware' },
    { id: 'event-id', name: 'Event' },
  ];

  const handleFilterChange = (filterName: string, value: any) => {
    console.log('Filter change:', filterName, value);
    
    switch (filterName) {
      case 'rating_min':
        setRatingMin(value);
        break;
      case 'rating_max':
        setRatingMax(value);
        break;
      case 'review_count_min':
        setReviewCountMin(value);
        break;
      case 'review_count_max':
        setReviewCountMax(value);
        break;
      case 'affiliate_status':
        setAffiliateStatus(value);
        break;
      case 'has_affiliate_link':
        setHasAffiliateLink(value);
        break;
      case 'integrations':
        setIntegrations(value || []);
        break;
      case 'platforms':
        setPlatforms(value || []);
        break;
      case 'targetAudience':
        setTargetAudience(value || []);
        break;
      case 'status':
        setStatus(value);
        break;
      case 'entity_type_filters':
        setEntityTypeFilters(value || {});
        break;
      default:
        console.warn('Unknown filter:', filterName);
    }
  };

  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">Filter Components Debug Page</h1>
      
      <div className="space-y-8">
        {/* Test 1: Rating Filter */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h2 className="text-xl font-semibold mb-4">1. Rating Filter Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <RatingFilter
                ratingMin={ratingMin}
                ratingMax={ratingMax}
                onRatingChange={handleFilterChange}
              />
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Current State:</h3>
              <pre className="text-sm">{JSON.stringify({ ratingMin, ratingMax }, null, 2)}</pre>
            </div>
          </div>
        </div>

        {/* Test 2: Review Count Filter */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h2 className="text-xl font-semibold mb-4">2. Review Count Filter Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <ReviewCountFilter
                reviewCountMin={reviewCountMin}
                reviewCountMax={reviewCountMax}
                onReviewCountChange={handleFilterChange}
              />
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Current State:</h3>
              <pre className="text-sm">{JSON.stringify({ reviewCountMin, reviewCountMax }, null, 2)}</pre>
            </div>
          </div>
        </div>

        {/* Test 3: Affiliate Filter */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h2 className="text-xl font-semibold mb-4">3. Affiliate Filter Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <AffiliateFilter
                affiliateStatus={affiliateStatus}
                hasAffiliateLink={hasAffiliateLink}
                onAffiliateChange={handleFilterChange}
              />
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Current State:</h3>
              <pre className="text-sm">{JSON.stringify({ affiliateStatus, hasAffiliateLink }, null, 2)}</pre>
            </div>
          </div>
        </div>

        {/* Test 4: Platform Integration Filter */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h2 className="text-xl font-semibold mb-4">4. Platform Integration Filter Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <PlatformIntegrationFilter
                integrations={integrations}
                platforms={platforms}
                targetAudience={targetAudience}
                onFilterChange={handleFilterChange}
              />
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Current State:</h3>
              <pre className="text-sm">{JSON.stringify({ integrations, platforms, targetAudience }, null, 2)}</pre>
            </div>
          </div>
        </div>

        {/* Test 5: Status Quality Filter */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h2 className="text-xl font-semibold mb-4">5. Status Quality Filter Test</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <StatusQualityFilter
                status={status}
                onFilterChange={handleFilterChange}
              />
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Current State:</h3>
              <pre className="text-sm">{JSON.stringify({ status }, null, 2)}</pre>
            </div>
          </div>
        </div>

        {/* Test 6: Entity Specific Filters */}
        <div className="bg-white p-6 rounded-lg shadow border">
          <h2 className="text-xl font-semibold mb-4">6. Entity Specific Filters Test</h2>
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">Select entity types to test:</p>
            <div className="flex gap-2">
              {mockEntityTypes.map(type => (
                <button
                  key={type.id}
                  className="px-3 py-1 text-sm border rounded hover:bg-gray-50"
                  onClick={() => {
                    // Toggle entity type selection for testing
                    console.log('Testing entity type:', type.name);
                  }}
                >
                  {type.name}
                </button>
              ))}
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <EntitySpecificFilters
                selectedEntityTypes={['course-id', 'job-id']} // Test with multiple types
                entityTypeFilters={entityTypeFilters}
                onFilterChange={handleFilterChange}
                allEntityTypes={mockEntityTypes}
              />
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium mb-2">Current State:</h3>
              <pre className="text-sm overflow-auto max-h-64">{JSON.stringify(entityTypeFilters, null, 2)}</pre>
            </div>
          </div>
        </div>

        {/* Overall State */}
        <div className="bg-blue-50 p-6 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">Overall Filter State</h2>
          <pre className="text-sm bg-white p-4 rounded border overflow-auto max-h-96">
{JSON.stringify({
  ratingMin,
  ratingMax,
  reviewCountMin,
  reviewCountMax,
  affiliateStatus,
  hasAffiliateLink,
  integrations,
  platforms,
  targetAudience,
  status,
  entityTypeFilters
}, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
