'use client';

import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X, Plus, Monitor, Smartphone, Globe, Code, Zap } from 'lucide-react';

interface PlatformIntegrationFilterProps {
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  onFilterChange: (filterName: 'integrations' | 'platforms' | 'targetAudience', value: string[] | null) => void;
}

// Common platforms
const COMMON_PLATFORMS = [
  { value: 'Web', label: 'Web Browser', icon: Globe },
  { value: 'Windows', label: 'Windows', icon: Monitor },
  { value: 'macOS', label: 'macOS', icon: Monitor },
  { value: 'Linux', label: 'Linux', icon: Monitor },
  { value: 'iOS', label: 'iOS', icon: Smartphone },
  { value: 'Android', label: 'Android', icon: Smartphone },
  { value: 'Chrome Extension', label: 'Chrome Extension', icon: Globe },
  { value: 'API', label: 'API/SDK', icon: Code },
];

// Common integrations
const COMMON_INTEGRATIONS = [
  'GitHub', 'Slack', 'Discord', 'Zapier', 'Google Workspace', 'Microsoft 365',
  'Notion', 'Trello', 'Asana', 'Jira', 'Figma', 'Adobe Creative Suite',
  'Salesforce', 'HubSpot', 'Shopify', 'WordPress', 'Stripe', 'PayPal'
];

// Target audiences
const TARGET_AUDIENCES = [
  'Developers', 'Designers', 'Marketers', 'Content Creators', 'Data Scientists',
  'Product Managers', 'Students', 'Researchers', 'Entrepreneurs', 'Small Business',
  'Enterprise', 'Freelancers', 'Agencies', 'Non-profits', 'Educators'
];

const PlatformIntegrationFilter: React.FC<PlatformIntegrationFilterProps> = ({
  integrations = [],
  platforms = [],
  targetAudience = [],
  onFilterChange,
}) => {
  const [customIntegration, setCustomIntegration] = useState('');
  const [customPlatform, setCustomPlatform] = useState('');
  const [customAudience, setCustomAudience] = useState('');

  const handleToggle = (filterName: 'integrations' | 'platforms' | 'targetAudience', value: string, currentValues: string[]) => {
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    onFilterChange(filterName, newValues.length > 0 ? newValues : null);
  };

  const handleAddCustom = (filterName: 'integrations' | 'platforms' | 'targetAudience', customValue: string, currentValues: string[]) => {
    if (customValue.trim() && !currentValues.includes(customValue.trim())) {
      const newValues = [...currentValues, customValue.trim()];
      onFilterChange(filterName, newValues);
      
      // Clear the input
      if (filterName === 'integrations') setCustomIntegration('');
      if (filterName === 'platforms') setCustomPlatform('');
      if (filterName === 'targetAudience') setCustomAudience('');
    }
  };

  const handleRemove = (filterName: 'integrations' | 'platforms' | 'targetAudience', value: string, currentValues: string[]) => {
    const newValues = currentValues.filter(v => v !== value);
    onFilterChange(filterName, newValues.length > 0 ? newValues : null);
  };

  return (
    <div className="space-y-6">
      {/* Platforms */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Monitor className="h-4 w-4 text-blue-500" />
          <Label className="text-sm font-medium">Platforms & OS</Label>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          {COMMON_PLATFORMS.map(platform => {
            const Icon = platform.icon;
            return (
              <Button
                key={platform.value}
                variant={platforms.includes(platform.value) ? "default" : "outline"}
                size="sm"
                onClick={() => handleToggle('platforms', platform.value, platforms)}
                className="justify-start h-8 text-xs"
              >
                <Icon className="h-3 w-3 mr-2" />
                {platform.label}
              </Button>
            );
          })}
        </div>

        {/* Custom platform input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom platform..."
            value={customPlatform}
            onChange={(e) => setCustomPlatform(e.target.value)}
            className="h-8 text-xs"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustom('platforms', customPlatform, platforms)}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAddCustom('platforms', customPlatform, platforms)}
            className="h-8 px-3"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {/* Selected platforms */}
        {platforms.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {platforms.map(platform => (
              <Badge
                key={platform}
                variant="secondary"
                className="text-xs cursor-pointer hover:bg-red-100"
                onClick={() => handleRemove('platforms', platform, platforms)}
              >
                {platform}
                <X className="ml-1 h-2 w-2" />
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Integrations */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Zap className="h-4 w-4 text-green-500" />
          <Label className="text-sm font-medium">Integrations</Label>
        </div>
        
        <div className="grid grid-cols-3 gap-1">
          {COMMON_INTEGRATIONS.map(integration => (
            <Button
              key={integration}
              variant={integrations.includes(integration) ? "default" : "outline"}
              size="sm"
              onClick={() => handleToggle('integrations', integration, integrations)}
              className="h-7 text-xs"
            >
              {integration}
            </Button>
          ))}
        </div>

        {/* Custom integration input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom integration..."
            value={customIntegration}
            onChange={(e) => setCustomIntegration(e.target.value)}
            className="h-8 text-xs"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustom('integrations', customIntegration, integrations)}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAddCustom('integrations', customIntegration, integrations)}
            className="h-8 px-3"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {/* Selected integrations */}
        {integrations.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {integrations.map(integration => (
              <Badge
                key={integration}
                variant="secondary"
                className="text-xs cursor-pointer hover:bg-red-100"
                onClick={() => handleRemove('integrations', integration, integrations)}
              >
                {integration}
                <X className="ml-1 h-2 w-2" />
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Target Audience */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Monitor className="h-4 w-4 text-purple-500" />
          <Label className="text-sm font-medium">Target Audience</Label>
        </div>
        
        <div className="grid grid-cols-3 gap-1">
          {TARGET_AUDIENCES.map(audience => (
            <Button
              key={audience}
              variant={targetAudience.includes(audience) ? "default" : "outline"}
              size="sm"
              onClick={() => handleToggle('targetAudience', audience, targetAudience)}
              className="h-7 text-xs"
            >
              {audience}
            </Button>
          ))}
        </div>

        {/* Custom audience input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom audience..."
            value={customAudience}
            onChange={(e) => setCustomAudience(e.target.value)}
            className="h-8 text-xs"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustom('targetAudience', customAudience, targetAudience)}
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAddCustom('targetAudience', customAudience, targetAudience)}
            className="h-8 px-3"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {/* Selected audiences */}
        {targetAudience.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {targetAudience.map(audience => (
              <Badge
                key={audience}
                variant="secondary"
                className="text-xs cursor-pointer hover:bg-red-100"
                onClick={() => handleRemove('targetAudience', audience, targetAudience)}
              >
                {audience}
                <X className="ml-1 h-2 w-2" />
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PlatformIntegrationFilter;
