'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDownIcon, ChevronUpIcon, SlidersHorizontal, Search, Star, DollarSign, Zap } from 'lucide-react';

// Import all filter components
import RatingFilter from './RatingFilter';
import ReviewCountFilter from './ReviewCountFilter';
import AffiliateFilter from './AffiliateFilter';
import PlatformIntegrationFilter from './PlatformIntegrationFilter';
import StatusQualityFilter from './StatusQualityFilter';
import AdvancedFilters from './AdvancedFilters';
import EntitySpecificFilters from './EntitySpecificFilters';

interface ComprehensiveFiltersProps {
  // All existing props from AdvancedFilters
  hasFreeTier?: boolean;
  apiAccess?: boolean;
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  createdAtFrom?: string;
  createdAtTo?: string;
  locationSearch?: string;
  ratingMin?: number;
  ratingMax?: number;
  reviewCountMin?: number;
  reviewCountMax?: number;
  affiliateStatus?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  hasAffiliateLink?: boolean;

  // New missing filters
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';

  // Entity-specific filters
  entityTypeFilters?: Record<string, any>;
  selectedEntityTypes?: string[];
  allEntityTypes?: Array<{ id: string; name: string }>;

  // Handlers
  onFilterChange: (filterName: string, value: boolean | string | string[] | number | null) => void;
  onClearAdvanced: () => void;
}

const ComprehensiveFilters: React.FC<ComprehensiveFiltersProps> = ({
  // Existing props
  hasFreeTier,
  apiAccess,
  employeeCountRanges,
  fundingStages,
  pricingModels,
  priceRanges,
  createdAtFrom,
  createdAtTo,
  locationSearch,
  ratingMin,
  ratingMax,
  reviewCountMin,
  reviewCountMax,
  affiliateStatus,
  hasAffiliateLink,
  
  // New props
  integrations,
  platforms,
  targetAudience,
  status,

  // Entity-specific props
  entityTypeFilters,
  selectedEntityTypes = [],
  allEntityTypes = [],

  onFilterChange,
  onClearAdvanced,
}) => {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    discovery: false,
    quality: false,
    business: false,
    technical: false,
  });

  // Count active filters for each category
  const discoveryFiltersCount = [
    integrations && integrations.length > 0 ? 1 : 0,
    platforms && platforms.length > 0 ? 1 : 0,
    targetAudience && targetAudience.length > 0 ? 1 : 0,
    locationSearch,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '' && filter !== 0).length;

  const qualityFiltersCount = [
    ratingMin,
    ratingMax,
    reviewCountMin,
    reviewCountMax,
    status,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '').length;

  const businessFiltersCount = [
    hasFreeTier,
    pricingModels && pricingModels.length > 0 ? 1 : 0,
    priceRanges && priceRanges.length > 0 ? 1 : 0,
    affiliateStatus,
    hasAffiliateLink,
    employeeCountRanges && employeeCountRanges.length > 0 ? 1 : 0,
    fundingStages && fundingStages.length > 0 ? 1 : 0,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '' && filter !== 0).length;

  const technicalFiltersCount = [
    apiAccess,
    createdAtFrom,
    createdAtTo,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '').length;

  // Count entity-specific filters
  const entitySpecificFiltersCount = entityTypeFilters ?
    Object.values(entityTypeFilters).reduce((total, entityFilters) =>
      total + Object.keys(entityFilters || {}).length, 0
    ) : 0;

  const totalActiveFilters = discoveryFiltersCount + qualityFiltersCount + businessFiltersCount + technicalFiltersCount + entitySpecificFiltersCount;

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const FilterSection = ({ 
    id, 
    title, 
    icon: Icon, 
    count, 
    children, 
    defaultOpen = false 
  }: { 
    id: string; 
    title: string; 
    icon: any; 
    count: number; 
    children: React.ReactNode; 
    defaultOpen?: boolean;
  }) => (
    <Collapsible open={openSections[id] || defaultOpen} onOpenChange={() => toggleSection(id)}>
      <CollapsibleTrigger asChild>
        <Button variant="outline" className="w-full justify-between h-auto p-3">
          <div className="flex items-center gap-2">
            <Icon className="h-4 w-4" />
            <span className="font-medium">{title}</span>
            {count > 0 && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {count}
              </Badge>
            )}
          </div>
          {openSections[id] ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="mt-3">
        <div className="bg-gray-50 p-4 rounded-lg space-y-4">
          {children}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <SlidersHorizontal className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Advanced Filters</h3>
          {totalActiveFilters > 0 && (
            <Badge variant="default" className="ml-2">
              {totalActiveFilters} active
            </Badge>
          )}
        </div>
        {totalActiveFilters > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearAdvanced}
            className="text-xs text-red-600 hover:text-red-700 hover:border-red-200"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Filter Categories */}
      <div className="space-y-3">
        {/* 1. DISCOVERY FILTERS */}
        <FilterSection
          id="discovery"
          title="Discovery & Targeting"
          icon={Search}
          count={discoveryFiltersCount}
        >
          <PlatformIntegrationFilter
            integrations={integrations}
            platforms={platforms}
            targetAudience={targetAudience}
            onFilterChange={onFilterChange}
          />
          
          {/* Location search */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Location</label>
            <input
              type="text"
              placeholder="e.g., San Francisco, Remote"
              value={locationSearch || ''}
              onChange={(e) => onFilterChange('locationSearch', e.target.value || null)}
              className="w-full px-3 py-2 text-sm border border-input rounded-md bg-background"
            />
          </div>
        </FilterSection>

        {/* 2. QUALITY & TRUST FILTERS */}
        <FilterSection
          id="quality"
          title="Quality & Trust"
          icon={Star}
          count={qualityFiltersCount}
        >
          <StatusQualityFilter
            status={status}
            onFilterChange={onFilterChange}
          />
          
          <RatingFilter
            ratingMin={ratingMin}
            ratingMax={ratingMax}
            onRatingChange={onFilterChange}
          />
          
          <ReviewCountFilter
            reviewCountMin={reviewCountMin}
            reviewCountMax={reviewCountMax}
            onReviewCountChange={onFilterChange}
          />
        </FilterSection>

        {/* 3. BUSINESS & PRICING FILTERS */}
        <FilterSection
          id="business"
          title="Business & Pricing"
          icon={DollarSign}
          count={businessFiltersCount}
        >
          <AdvancedFilters
            hasFreeTier={hasFreeTier}
            apiAccess={false} // We'll handle this in technical section
            employeeCountRanges={employeeCountRanges}
            fundingStages={fundingStages}
            pricingModels={pricingModels}
            priceRanges={priceRanges}
            createdAtFrom={undefined} // We'll handle this in technical section
            createdAtTo={undefined}
            locationSearch={undefined} // Already handled in discovery
            ratingMin={undefined} // Already handled in quality
            ratingMax={undefined}
            reviewCountMin={undefined}
            reviewCountMax={undefined}
            affiliateStatus={affiliateStatus}
            hasAffiliateLink={hasAffiliateLink}
            onFilterChange={onFilterChange}
            onClearAdvanced={() => {}} // We handle clearing at the top level
          />
        </FilterSection>

        {/* 4. TECHNICAL FILTERS */}
        <FilterSection
          id="technical"
          title="Technical & Timeline"
          icon={Zap}
          count={technicalFiltersCount}
        >
          {/* API Access */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="apiAccess"
              checked={apiAccess || false}
              onChange={(e) => onFilterChange('apiAccess', e.target.checked || null)}
              className="rounded border-gray-300"
            />
            <label htmlFor="apiAccess" className="text-sm">API Access Available</label>
          </div>

          {/* Date filters */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Date Range</h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">From</label>
                <input
                  type="date"
                  value={createdAtFrom || ''}
                  onChange={(e) => onFilterChange('createdAtFrom', e.target.value || null)}
                  className="w-full px-2 py-1 text-xs border border-input rounded-md bg-background"
                />
              </div>
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">To</label>
                <input
                  type="date"
                  value={createdAtTo || ''}
                  onChange={(e) => onFilterChange('createdAtTo', e.target.value || null)}
                  className="w-full px-2 py-1 text-xs border border-input rounded-md bg-background"
                />
              </div>
            </div>
          </div>
        </FilterSection>

        {/* 5. ENTITY-SPECIFIC FILTERS */}
        {selectedEntityTypes.length > 0 && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-2 border-dashed border-blue-200">
            <div className="flex items-center gap-2 mb-4">
              <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <h3 className="text-lg font-semibold text-blue-900">Smart Type-Specific Filters</h3>
              {entitySpecificFiltersCount > 0 && (
                <Badge variant="default" className="bg-blue-600">
                  {entitySpecificFiltersCount} active
                </Badge>
              )}
            </div>
            <p className="text-sm text-blue-700 mb-4">
              Advanced filters tailored to your selected resource types for precise discovery.
            </p>
            <EntitySpecificFilters
              selectedEntityTypes={selectedEntityTypes}
              entityTypeFilters={entityTypeFilters || {}}
              onFilterChange={onFilterChange}
              allEntityTypes={allEntityTypes}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ComprehensiveFilters;
