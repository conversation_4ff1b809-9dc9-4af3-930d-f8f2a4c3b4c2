'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import AffiliateFilter from './AffiliateFilter';

interface BusinessFiltersProps {
  hasFreeTier?: boolean;
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  affiliateStatus?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  hasAffiliateLink?: boolean;
  onFilterChange: (filterName: string, value: boolean | string | string[] | number | null) => void;
}

const PRICING_MODELS = [
  { value: 'FREE', label: 'Free' },
  { value: 'FREEMIUM', label: 'Freemium' },
  { value: 'SUBSCRIPTION', label: 'Subscription' },
  { value: 'PAY_PER_USE', label: 'Pay Per Use' },
  { value: 'ONE_TIME_PURCHASE', label: 'One-time Purchase' },
  { value: 'CONTACT_SALES', label: 'Contact Sales' },
  { value: 'OPEN_SOURCE', label: 'Open Source' },
];

const PRICE_RANGES = [
  { value: 'FREE', label: 'Free' },
  { value: 'LOW', label: 'Low' },
  { value: 'MEDIUM', label: 'Medium' },
  { value: 'HIGH', label: 'High' },
  { value: 'ENTERPRISE', label: 'Enterprise' },
];

const EMPLOYEE_COUNT_RANGES = [
  { value: 'C1_10', label: '1-10 employees' },
  { value: 'C11_50', label: '11-50 employees' },
  { value: 'C51_200', label: '51-200 employees' },
  { value: 'C201_500', label: '201-500 employees' },
  { value: 'C501_1000', label: '501-1000 employees' },
  { value: 'C1001_5000', label: '1001-5000 employees' },
  { value: 'C5001_PLUS', label: '5000+ employees' },
];

const FUNDING_STAGES = [
  { value: 'SEED', label: 'Seed' },
  { value: 'PRE_SEED', label: 'Pre-Seed' },
  { value: 'SERIES_A', label: 'Series A' },
  { value: 'SERIES_B', label: 'Series B' },
  { value: 'SERIES_C', label: 'Series C' },
  { value: 'SERIES_D_PLUS', label: 'Series D+' },
  { value: 'PUBLIC', label: 'Public' },
];

const BusinessFilters: React.FC<BusinessFiltersProps> = ({
  hasFreeTier,
  employeeCountRanges,
  fundingStages,
  pricingModels,
  priceRanges,
  affiliateStatus,
  hasAffiliateLink,
  onFilterChange,
}) => {
  const handleBooleanChange = (filterName: string, checked: boolean) => {
    onFilterChange(filterName, checked || null);
  };

  const handleMultiSelectChange = (filterName: string, value: string, currentValues: string[] = []) => {
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];

    onFilterChange(filterName, newValues.length > 0 ? newValues : null);
  };

  return (
    <div className="space-y-6">
      {/* Free Tier */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="hasFreeTier"
          checked={hasFreeTier || false}
          onCheckedChange={(checked) => handleBooleanChange('hasFreeTier', checked as boolean)}
        />
        <Label htmlFor="hasFreeTier" className="text-sm">Has Free Tier</Label>
      </div>

      {/* Pricing Models */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Pricing Model</Label>
        <div className="grid grid-cols-2 gap-2">
          {PRICING_MODELS.map(model => (
            <div key={model.value} className="flex items-center space-x-2">
              <Checkbox
                id={`pricing-${model.value}`}
                checked={pricingModels?.includes(model.value) || false}
                onCheckedChange={() => handleMultiSelectChange('pricingModels', model.value, pricingModels || [])}
              />
              <Label htmlFor={`pricing-${model.value}`} className="text-xs">
                {model.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Price Ranges */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Price Range</Label>
        <div className="grid grid-cols-2 gap-2">
          {PRICE_RANGES.map(range => (
            <div key={range.value} className="flex items-center space-x-2">
              <Checkbox
                id={`price-${range.value}`}
                checked={priceRanges?.includes(range.value) || false}
                onCheckedChange={() => handleMultiSelectChange('priceRanges', range.value, priceRanges || [])}
              />
              <Label htmlFor={`price-${range.value}`} className="text-xs">
                {range.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Employee Count Ranges */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Company Size</Label>
        <div className="grid grid-cols-1 gap-2">
          {EMPLOYEE_COUNT_RANGES.map(range => (
            <div key={range.value} className="flex items-center space-x-2">
              <Checkbox
                id={`employee-${range.value}`}
                checked={employeeCountRanges?.includes(range.value) || false}
                onCheckedChange={() => handleMultiSelectChange('employeeCountRanges', range.value, employeeCountRanges || [])}
              />
              <Label htmlFor={`employee-${range.value}`} className="text-xs">
                {range.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Funding Stages */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Funding Stage</Label>
        <div className="grid grid-cols-2 gap-2">
          {FUNDING_STAGES.map(stage => (
            <div key={stage.value} className="flex items-center space-x-2">
              <Checkbox
                id={`funding-${stage.value}`}
                checked={fundingStages?.includes(stage.value) || false}
                onCheckedChange={() => handleMultiSelectChange('fundingStages', stage.value, fundingStages || [])}
              />
              <Label htmlFor={`funding-${stage.value}`} className="text-xs">
                {stage.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Affiliate Filters */}
      <AffiliateFilter
        affiliateStatus={affiliateStatus}
        hasAffiliateLink={hasAffiliateLink}
        onAffiliateChange={onFilterChange}
      />
    </div>
  );
};

export default BusinessFilters;
