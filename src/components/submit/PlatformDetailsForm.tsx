'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface PlatformDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function PlatformDetailsForm({ register, errors, setValue, watch }: PlatformDetailsFormProps) {
  const hasFreeTier = watch('details.has_free_tier');
  const hasApi = watch('details.has_api');
  const hasLiveChat = watch('details.has_live_chat');
  const apiAccess = watch('details.api_access');
  const demoAvailable = watch('details.demo_available');
  const trialAvailable = watch('details.trial_available');
  const mobileSupport = watch('details.mobile_support');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Platform Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Platform Type */}
        <div>
          <Label htmlFor="platform_type">Platform Type</Label>
          <Input
            id="platform_type"
            {...register('details.platform_type')}
            placeholder="SaaS, PaaS, IaaS, Development Platform..."
            className="mt-1"
          />
        </div>

        {/* Documentation URL */}
        <div>
          <Label htmlFor="documentation_url">Documentation URL</Label>
          <Input
            id="documentation_url"
            type="url"
            {...register('details.documentation_url')}
            placeholder="https://docs.example.com"
            className="mt-1"
          />
        </div>

        {/* Community URL */}
        <div>
          <Label htmlFor="community_url">Community URL</Label>
          <Input
            id="community_url"
            type="url"
            {...register('details.community_url')}
            placeholder="https://community.example.com"
            className="mt-1"
          />
        </div>

        {/* Pricing Model */}
        <div>
          <Label htmlFor="pricing_model">Pricing Model</Label>
          <Select onValueChange={(value) => setValue('details.pricing_model', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select pricing model..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="FREEMIUM">Freemium</SelectItem>
              <SelectItem value="PAID">Paid</SelectItem>
              <SelectItem value="SUBSCRIPTION">Subscription</SelectItem>
              <SelectItem value="ONE_TIME">One-time Purchase</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Price Range */}
        <div>
          <Label htmlFor="price_range">Price Range</Label>
          <Select onValueChange={(value) => setValue('details.price_range', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select price range..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="LOW">Low ($1-$50/month)</SelectItem>
              <SelectItem value="MEDIUM">Medium ($51-$200/month)</SelectItem>
              <SelectItem value="HIGH">High ($201-$1000/month)</SelectItem>
              <SelectItem value="ENTERPRISE">Enterprise ($1000+/month)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Customization Level */}
        <div>
          <Label htmlFor="customization_level">Customization Level</Label>
          <Input
            id="customization_level"
            {...register('details.customization_level')}
            placeholder="High, Medium, Low, None"
            className="mt-1"
          />
        </div>

        {/* Pricing URL */}
        <div>
          <Label htmlFor="pricing_url">Pricing URL</Label>
          <Input
            id="pricing_url"
            type="url"
            {...register('details.pricing_url')}
            placeholder="https://example.com/pricing"
            className="mt-1"
          />
        </div>

        {/* Support Email */}
        <div>
          <Label htmlFor="support_email">Support Email</Label>
          <Input
            id="support_email"
            type="email"
            {...register('details.support_email')}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>
      </div>

      {/* Boolean Options */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_free_tier"
              checked={hasFreeTier}
              onCheckedChange={(checked) => setValue('details.has_free_tier', checked)}
            />
            <Label htmlFor="has_free_tier">Has Free Tier</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_api"
              checked={hasApi}
              onCheckedChange={(checked) => setValue('details.has_api', checked)}
            />
            <Label htmlFor="has_api">Has API</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_live_chat"
              checked={hasLiveChat}
              onCheckedChange={(checked) => setValue('details.has_live_chat', checked)}
            />
            <Label htmlFor="has_live_chat">Live Chat Support</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="api_access"
              checked={apiAccess}
              onCheckedChange={(checked) => setValue('details.api_access', checked)}
            />
            <Label htmlFor="api_access">API Access</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="demo_available"
              checked={demoAvailable}
              onCheckedChange={(checked) => setValue('details.demo_available', checked)}
            />
            <Label htmlFor="demo_available">Demo Available</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="trial_available"
              checked={trialAvailable}
              onCheckedChange={(checked) => setValue('details.trial_available', checked)}
            />
            <Label htmlFor="trial_available">Trial Available</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="mobile_support"
              checked={mobileSupport}
              onCheckedChange={(checked) => setValue('details.mobile_support', checked)}
            />
            <Label htmlFor="mobile_support">Mobile Support</Label>
          </div>
        </div>
      </div>

      {/* Pricing Details */}
      <div>
        <Label htmlFor="pricing_details">Pricing Details</Label>
        <Textarea
          id="pricing_details"
          {...register('details.pricing_details')}
          placeholder="Detailed pricing information, plans, features included..."
          className="mt-1 min-h-[80px]"
        />
      </div>

      {/* Array Fields */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="deployment_options">Deployment Options (comma-separated)</Label>
          <Textarea
            id="deployment_options"
            {...register('details.deployment_options_text')}
            placeholder="Cloud, On-premise, Hybrid, Multi-cloud..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter deployment options separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="target_audience">Target Audience (comma-separated)</Label>
          <Textarea
            id="target_audience"
            {...register('details.target_audience_text')}
            placeholder="Developers, DevOps, Enterprises, Startups..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter target audiences separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="supported_os">Supported Operating Systems (comma-separated)</Label>
          <Textarea
            id="supported_os"
            {...register('details.supported_os_text')}
            placeholder="Windows, macOS, Linux, Web-based..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter supported operating systems separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="integrations">Integrations (comma-separated)</Label>
          <Textarea
            id="integrations"
            {...register('details.integrations_text')}
            placeholder="AWS, Azure, GCP, GitHub, Slack..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter available integrations separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="key_services">Key Services (comma-separated)</Label>
          <Textarea
            id="key_services"
            {...register('details.key_services_text')}
            placeholder="Hosting, Analytics, Monitoring, CI/CD..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter key services provided separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="use_cases">Use Cases (comma-separated)</Label>
          <Textarea
            id="use_cases"
            {...register('details.use_cases_text')}
            placeholder="Web development, Data processing, Machine learning..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter use cases separated by commas.
          </p>
        </div>
      </div>
    </div>
  );
}
