'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface ModelDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function ModelDetailsForm({ register, errors, setValue, watch }: ModelDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">AI Model Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Model Architecture */}
        <div>
          <Label htmlFor="model_architecture">Model Architecture</Label>
          <Input
            id="model_architecture"
            {...register('details.model_architecture')}
            placeholder="Transformer, CNN, RNN, GPT, BERT..."
            className="mt-1"
          />
        </div>

        {/* Training Dataset */}
        <div>
          <Label htmlFor="training_dataset">Training Dataset</Label>
          <Input
            id="training_dataset"
            {...register('details.training_dataset')}
            placeholder="Common Crawl, ImageNet, LAION..."
            className="mt-1"
          />
        </div>

        {/* License */}
        <div>
          <Label htmlFor="license">License</Label>
          <Input
            id="license"
            {...register('details.license')}
            placeholder="MIT, Apache 2.0, Custom, Commercial..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Performance Metrics */}
      <div>
        <Label htmlFor="performance_metrics">Performance Metrics (JSON format)</Label>
        <Textarea
          id="performance_metrics"
          {...register('details.performance_metrics')}
          placeholder='{"accuracy": 0.95, "f1_score": 0.92, "perplexity": 15.2, "bleu_score": 0.88}'
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter performance metrics as JSON object with metric names and values.
        </p>
      </div>

      {/* Array Fields */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="input_data_types">Input Data Types (comma-separated)</Label>
          <Textarea
            id="input_data_types"
            {...register('details.input_data_types_text')}
            placeholder="Text, Image, Audio, Video, Tabular..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter input data types separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="output_data_types">Output Data Types (comma-separated)</Label>
          <Textarea
            id="output_data_types"
            {...register('details.output_data_types_text')}
            placeholder="Text, Classification, Embeddings, Predictions..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter output data types separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="frameworks">Frameworks (comma-separated)</Label>
          <Textarea
            id="frameworks"
            {...register('details.frameworks_text')}
            placeholder="PyTorch, TensorFlow, Hugging Face, JAX..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter supported frameworks separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="libraries">Libraries (comma-separated)</Label>
          <Textarea
            id="libraries"
            {...register('details.libraries_text')}
            placeholder="Transformers, OpenAI, LangChain, scikit-learn..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter required libraries separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="deployment_options">Deployment Options (comma-separated)</Label>
          <Textarea
            id="deployment_options"
            {...register('details.deployment_options_text')}
            placeholder="Cloud API, Local inference, Edge deployment, Docker..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter deployment options separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="use_cases">Use Cases (comma-separated)</Label>
          <Textarea
            id="use_cases"
            {...register('details.use_cases_text')}
            placeholder="Text generation, Image classification, Sentiment analysis..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter use cases separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="target_audience">Target Audience (comma-separated)</Label>
          <Textarea
            id="target_audience"
            {...register('details.target_audience_text')}
            placeholder="Researchers, Developers, Data scientists, Students..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter target audiences separated by commas.
          </p>
        </div>
      </div>
    </div>
  );
}
