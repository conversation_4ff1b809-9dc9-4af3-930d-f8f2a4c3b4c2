'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface ResearchPaperDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function ResearchPaperDetailsForm({ register, errors, setValue, watch }: ResearchPaperDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Research Paper Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* DOI */}
        <div>
          <Label htmlFor="doi">DOI</Label>
          <Input
            id="doi"
            {...register('details.doi')}
            placeholder="10.1000/182"
            className="mt-1"
          />
        </div>

        {/* Journal or Conference */}
        <div>
          <Label htmlFor="journal_or_conference">Journal or Conference</Label>
          <Input
            id="journal_or_conference"
            {...register('details.journal_or_conference')}
            placeholder="Nature, ICML, NeurIPS, ICLR..."
            className="mt-1"
          />
        </div>

        {/* Publication Date */}
        <div>
          <Label htmlFor="publication_date">Publication Date</Label>
          <Input
            id="publication_date"
            type="date"
            {...register('details.publication_date')}
            className="mt-1"
          />
        </div>

        {/* Citation Count */}
        <div>
          <Label htmlFor="citation_count">Citation Count</Label>
          <Input
            id="citation_count"
            type="number"
            {...register('details.citation_count', { valueAsNumber: true })}
            placeholder="150"
            className="mt-1"
          />
        </div>

        {/* PDF URL */}
        <div className="md:col-span-2">
          <Label htmlFor="pdf_url">PDF URL</Label>
          <Input
            id="pdf_url"
            type="url"
            {...register('details.pdf_url')}
            placeholder="https://arxiv.org/pdf/2301.00001.pdf"
            className="mt-1"
          />
        </div>
      </div>

      {/* Abstract */}
      <div>
        <Label htmlFor="abstract">Abstract</Label>
        <Textarea
          id="abstract"
          {...register('details.abstract')}
          placeholder="Enter the paper's abstract here..."
          className="mt-1 min-h-[120px]"
        />
      </div>

      {/* Authors */}
      <div>
        <Label htmlFor="authors">Authors (comma-separated)</Label>
        <Textarea
          id="authors"
          {...register('details.authors_text')}
          placeholder="John Smith, Jane Doe, Alice Johnson..."
          className="mt-1 min-h-[60px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter author names separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
