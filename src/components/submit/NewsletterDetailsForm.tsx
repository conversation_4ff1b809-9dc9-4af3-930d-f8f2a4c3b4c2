'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface NewsletterDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function NewsletterDetailsForm({ register, errors, setValue, watch }: NewsletterDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Newsletter Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Author Name */}
        <div>
          <Label htmlFor="author_name">Author Name</Label>
          <Input
            id="author_name"
            {...register('details.author_name')}
            placeholder="<PERSON>"
            className="mt-1"
          />
        </div>

        {/* Frequency */}
        <div>
          <Label htmlFor="frequency">Frequency</Label>
          <Input
            id="frequency"
            {...register('details.frequency')}
            placeholder="Weekly, Bi-weekly, Monthly..."
            className="mt-1"
          />
        </div>

        {/* Archive URL */}
        <div>
          <Label htmlFor="archive_url">Archive URL</Label>
          <Input
            id="archive_url"
            type="url"
            {...register('details.archive_url')}
            placeholder="https://newsletter.com/archive"
            className="mt-1"
          />
        </div>

        {/* Subscribe URL */}
        <div>
          <Label htmlFor="subscribe_url">Subscribe URL</Label>
          <Input
            id="subscribe_url"
            type="url"
            {...register('details.subscribe_url')}
            placeholder="https://newsletter.com/subscribe"
            className="mt-1"
          />
        </div>

        {/* Subscriber Count */}
        <div>
          <Label htmlFor="subscriber_count">Subscriber Count</Label>
          <Input
            id="subscriber_count"
            type="number"
            {...register('details.subscriber_count', { valueAsNumber: true })}
            placeholder="10000"
            className="mt-1"
          />
        </div>
      </div>

      {/* Target Audience */}
      <div>
        <Label htmlFor="target_audience">Target Audience</Label>
        <Input
          id="target_audience"
          {...register('details.target_audience')}
          placeholder="Developers, Entrepreneurs, AI enthusiasts..."
          className="mt-1"
        />
      </div>

      {/* Main Topics */}
      <div>
        <Label htmlFor="main_topics">Main Topics (comma-separated)</Label>
        <Textarea
          id="main_topics"
          {...register('details.main_topics_text')}
          placeholder="AI news, Tech trends, Industry insights, Product reviews..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter topics separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
