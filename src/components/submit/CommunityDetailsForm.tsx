'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface CommunityDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function CommunityDetailsForm({ register, errors, setValue, watch }: CommunityDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Community Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Platform */}
        <div>
          <Label htmlFor="platform">Platform</Label>
          <Select onValueChange={(value) => setValue('details.platform', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select platform..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Discord">Discord</SelectItem>
              <SelectItem value="Slack">Slack</SelectItem>
              <SelectItem value="Reddit">Reddit</SelectItem>
              <SelectItem value="Telegram">Telegram</SelectItem>
              <SelectItem value="Facebook">Facebook</SelectItem>
              <SelectItem value="LinkedIn">LinkedIn</SelectItem>
              <SelectItem value="Forum">Forum</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Member Count */}
        <div>
          <Label htmlFor="member_count">Member Count</Label>
          <Input
            id="member_count"
            type="number"
            {...register('details.member_count', { valueAsNumber: true })}
            placeholder="1000"
            className="mt-1"
          />
        </div>

        {/* Invite URL */}
        <div>
          <Label htmlFor="invite_url">Invite URL</Label>
          <Input
            id="invite_url"
            type="url"
            {...register('details.invite_url')}
            placeholder="https://discord.gg/..."
            className="mt-1"
          />
        </div>

        {/* Main Channel URL */}
        <div>
          <Label htmlFor="main_channel_url">Main Channel URL</Label>
          <Input
            id="main_channel_url"
            type="url"
            {...register('details.main_channel_url')}
            placeholder="https://discord.com/channels/..."
            className="mt-1"
          />
        </div>

        {/* Rules URL */}
        <div className="md:col-span-2">
          <Label htmlFor="rules_url">Rules URL</Label>
          <Input
            id="rules_url"
            type="url"
            {...register('details.rules_url')}
            placeholder="https://community.com/rules"
            className="mt-1"
          />
        </div>
      </div>

      {/* Focus Topics */}
      <div>
        <Label htmlFor="focus_topics">Focus Topics (comma-separated)</Label>
        <Textarea
          id="focus_topics"
          {...register('details.focus_topics_text')}
          placeholder="AI, Machine Learning, Deep Learning, NLP, Computer Vision..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter focus topics separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
