'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface EventDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function EventDetailsForm({ register, errors, setValue, watch }: EventDetailsFormProps) {
  const isOnline = watch('details.is_online');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Event Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Start Date */}
        <div>
          <Label htmlFor="start_date">Start Date</Label>
          <Input
            id="start_date"
            type="date"
            {...register('details.start_date')}
            className="mt-1"
          />
        </div>

        {/* End Date */}
        <div>
          <Label htmlFor="end_date">End Date</Label>
          <Input
            id="end_date"
            type="date"
            {...register('details.end_date')}
            className="mt-1"
          />
        </div>

        {/* Location */}
        <div>
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            {...register('details.location')}
            placeholder="San Francisco, CA | Online | Hybrid"
            className="mt-1"
          />
        </div>

        {/* Event Type */}
        <div>
          <Label htmlFor="event_type">Event Type</Label>
          <Select onValueChange={(value) => setValue('details.event_type', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select event type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Conference">Conference</SelectItem>
              <SelectItem value="Workshop">Workshop</SelectItem>
              <SelectItem value="Meetup">Meetup</SelectItem>
              <SelectItem value="Webinar">Webinar</SelectItem>
              <SelectItem value="Hackathon">Hackathon</SelectItem>
              <SelectItem value="Summit">Summit</SelectItem>
              <SelectItem value="Training">Training</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Price */}
        <div>
          <Label htmlFor="price">Price</Label>
          <Input
            id="price"
            {...register('details.price')}
            placeholder="Free, $50, $500, $1000..."
            className="mt-1"
          />
        </div>

        {/* Registration URL */}
        <div>
          <Label htmlFor="registration_url">Registration URL</Label>
          <Input
            id="registration_url"
            type="url"
            {...register('details.registration_url')}
            placeholder="https://event.com/register"
            className="mt-1"
          />
        </div>
      </div>

      {/* Online Event */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="is_online"
          checked={isOnline}
          onCheckedChange={(checked) => setValue('details.is_online', checked)}
        />
        <Label htmlFor="is_online">Online Event</Label>
      </div>

      {/* Key Speakers */}
      <div>
        <Label htmlFor="key_speakers">Key Speakers (comma-separated)</Label>
        <Textarea
          id="key_speakers"
          {...register('details.key_speakers_text')}
          placeholder="Andrew Ng, Yann LeCun, Fei-Fei Li, Geoffrey Hinton..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter speaker names separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Target Audience */}
      <div>
        <Label htmlFor="target_audience">Target Audience (comma-separated)</Label>
        <Textarea
          id="target_audience"
          {...register('details.target_audience_text')}
          placeholder="AI Researchers, Data Scientists, ML Engineers, Students..."
          className="mt-1 min-h-[60px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter target audiences separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Topics */}
      <div>
        <Label htmlFor="topics">Topics (comma-separated)</Label>
        <Textarea
          id="topics"
          {...register('details.topics_text')}
          placeholder="Machine Learning, Deep Learning, NLP, Computer Vision, Robotics..."
          className="mt-1 min-h-[60px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter topics separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
