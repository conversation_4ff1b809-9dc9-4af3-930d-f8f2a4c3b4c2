'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface HardwareDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function HardwareDetailsForm({ register, errors, setValue, watch }: HardwareDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">AI Hardware Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* GPU */}
        <div>
          <Label htmlFor="gpu">GPU</Label>
          <Input
            id="gpu"
            {...register('details.gpu')}
            placeholder="RTX 4090, H100, A100, V100..."
            className="mt-1"
          />
        </div>

        {/* Processor */}
        <div>
          <Label htmlFor="processor">Processor</Label>
          <Input
            id="processor"
            {...register('details.processor')}
            placeholder="Intel Xeon, AMD EPYC, Apple M2..."
            className="mt-1"
          />
        </div>

        {/* Memory */}
        <div>
          <Label htmlFor="memory">Memory</Label>
          <Input
            id="memory"
            {...register('details.memory')}
            placeholder="32GB, 64GB, 128GB VRAM..."
            className="mt-1"
          />
        </div>

        {/* Storage */}
        <div>
          <Label htmlFor="storage">Storage</Label>
          <Input
            id="storage"
            {...register('details.storage')}
            placeholder="1TB NVMe SSD, 2TB..."
            className="mt-1"
          />
        </div>

        {/* Power Consumption */}
        <div>
          <Label htmlFor="power_consumption">Power Consumption</Label>
          <Input
            id="power_consumption"
            {...register('details.power_consumption')}
            placeholder="450W, 700W, 1000W..."
            className="mt-1"
          />
        </div>

        {/* Price */}
        <div>
          <Label htmlFor="price">Price</Label>
          <Input
            id="price"
            {...register('details.price')}
            placeholder="$1,500, $10,000, $50,000..."
            className="mt-1"
          />
        </div>

        {/* Availability */}
        <div className="md:col-span-2">
          <Label htmlFor="availability">Availability</Label>
          <Input
            id="availability"
            {...register('details.availability')}
            placeholder="In stock, Pre-order, Limited availability..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Use Cases */}
      <div>
        <Label htmlFor="use_cases">Use Cases (comma-separated)</Label>
        <Textarea
          id="use_cases"
          {...register('details.use_cases_text')}
          placeholder="AI Training, Inference, Gaming, Cryptocurrency mining, Scientific computing..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter use cases separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
