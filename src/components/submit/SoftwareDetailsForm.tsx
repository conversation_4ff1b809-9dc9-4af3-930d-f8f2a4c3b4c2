'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface SoftwareDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function SoftwareDetailsForm({ register, errors, setValue, watch }: SoftwareDetailsFormProps) {
  const hasFreeTier = watch('details.has_free_tier');
  const hasApi = watch('details.has_api');
  const hasLiveChat = watch('details.has_live_chat');
  const apiAccess = watch('details.api_access');
  const demoAvailable = watch('details.demo_available');
  const trialAvailable = watch('details.trial_available');
  const openSource = watch('details.open_source');
  const mobileSupport = watch('details.mobile_support');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Software Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Version */}
        <div>
          <Label htmlFor="current_version">Current Version</Label>
          <Input
            id="current_version"
            {...register('details.current_version')}
            placeholder="v2.1.0"
            className="mt-1"
          />
        </div>

        {/* License Type */}
        <div>
          <Label htmlFor="license_type">License Type</Label>
          <Input
            id="license_type"
            {...register('details.license_type')}
            placeholder="MIT, Apache 2.0, Proprietary..."
            className="mt-1"
          />
        </div>

        {/* Community URL */}
        <div>
          <Label htmlFor="community_url">Community URL</Label>
          <Input
            id="community_url"
            type="url"
            {...register('details.community_url')}
            placeholder="https://community.example.com"
            className="mt-1"
          />
        </div>

        {/* Pricing Model */}
        <div>
          <Label htmlFor="pricing_model">Pricing Model</Label>
          <Select onValueChange={(value) => setValue('details.pricing_model', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select pricing model..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="FREEMIUM">Freemium</SelectItem>
              <SelectItem value="PAID">Paid</SelectItem>
              <SelectItem value="SUBSCRIPTION">Subscription</SelectItem>
              <SelectItem value="ONE_TIME">One-time Purchase</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Price Range */}
        <div>
          <Label htmlFor="price_range">Price Range</Label>
          <Select onValueChange={(value) => setValue('details.price_range', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select price range..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="LOW">Low ($1-$50/month)</SelectItem>
              <SelectItem value="MEDIUM">Medium ($51-$200/month)</SelectItem>
              <SelectItem value="HIGH">High ($201-$1000/month)</SelectItem>
              <SelectItem value="ENTERPRISE">Enterprise ($1000+/month)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Customization Level */}
        <div>
          <Label htmlFor="customization_level">Customization Level</Label>
          <Input
            id="customization_level"
            {...register('details.customization_level')}
            placeholder="High, Medium, Low, None"
            className="mt-1"
          />
        </div>

        {/* Pricing URL */}
        <div>
          <Label htmlFor="pricing_url">Pricing URL</Label>
          <Input
            id="pricing_url"
            type="url"
            {...register('details.pricing_url')}
            placeholder="https://example.com/pricing"
            className="mt-1"
          />
        </div>

        {/* Support Email */}
        <div>
          <Label htmlFor="support_email">Support Email</Label>
          <Input
            id="support_email"
            type="email"
            {...register('details.support_email')}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>
      </div>

      {/* Boolean Options */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_free_tier"
              checked={hasFreeTier}
              onCheckedChange={(checked) => setValue('details.has_free_tier', checked)}
            />
            <Label htmlFor="has_free_tier">Has Free Tier</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_api"
              checked={hasApi}
              onCheckedChange={(checked) => setValue('details.has_api', checked)}
            />
            <Label htmlFor="has_api">Has API</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_live_chat"
              checked={hasLiveChat}
              onCheckedChange={(checked) => setValue('details.has_live_chat', checked)}
            />
            <Label htmlFor="has_live_chat">Live Chat Support</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="api_access"
              checked={apiAccess}
              onCheckedChange={(checked) => setValue('details.api_access', checked)}
            />
            <Label htmlFor="api_access">API Access</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="demo_available"
              checked={demoAvailable}
              onCheckedChange={(checked) => setValue('details.demo_available', checked)}
            />
            <Label htmlFor="demo_available">Demo Available</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="trial_available"
              checked={trialAvailable}
              onCheckedChange={(checked) => setValue('details.trial_available', checked)}
            />
            <Label htmlFor="trial_available">Trial Available</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="open_source"
              checked={openSource}
              onCheckedChange={(checked) => setValue('details.open_source', checked)}
            />
            <Label htmlFor="open_source">Open Source</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="mobile_support"
              checked={mobileSupport}
              onCheckedChange={(checked) => setValue('details.mobile_support', checked)}
            />
            <Label htmlFor="mobile_support">Mobile Support</Label>
          </div>
        </div>
      </div>

      {/* Pricing Details */}
      <div>
        <Label htmlFor="pricing_details">Pricing Details</Label>
        <Textarea
          id="pricing_details"
          {...register('details.pricing_details')}
          placeholder="Detailed pricing information, plans, features included..."
          className="mt-1 min-h-[80px]"
        />
      </div>

      {/* Array Fields */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="key_features">Key Features (comma-separated)</Label>
          <Textarea
            id="key_features"
            {...register('details.key_features_text')}
            placeholder="Advanced analytics, Real-time collaboration, Cloud deployment..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter features separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="frameworks">Frameworks (comma-separated)</Label>
          <Textarea
            id="frameworks"
            {...register('details.frameworks_text')}
            placeholder="React, Vue, Angular, Django, Flask..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter supported frameworks separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="libraries">Libraries (comma-separated)</Label>
          <Textarea
            id="libraries"
            {...register('details.libraries_text')}
            placeholder="TensorFlow, PyTorch, NumPy, Pandas..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter supported libraries separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="programming_languages">Programming Languages (comma-separated)</Label>
          <Textarea
            id="programming_languages"
            {...register('details.programming_languages_text')}
            placeholder="Python, JavaScript, Java, C++, Go..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter supported programming languages separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="integrations">Integrations (comma-separated)</Label>
          <Textarea
            id="integrations"
            {...register('details.integrations_text')}
            placeholder="Slack, Discord, Zapier, GitHub, Jira..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter available integrations separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="platform_compatibility">Platform Compatibility (comma-separated)</Label>
          <Textarea
            id="platform_compatibility"
            {...register('details.platform_compatibility_text')}
            placeholder="Web, Desktop, Mobile, Cloud..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter platform compatibility separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="supported_os">Supported Operating Systems (comma-separated)</Label>
          <Textarea
            id="supported_os"
            {...register('details.supported_os_text')}
            placeholder="Windows, macOS, Linux, iOS, Android..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter supported operating systems separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="deployment_options">Deployment Options (comma-separated)</Label>
          <Textarea
            id="deployment_options"
            {...register('details.deployment_options_text')}
            placeholder="Cloud, On-premise, Hybrid, SaaS, Self-hosted..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter deployment options separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="support_channels">Support Channels (comma-separated)</Label>
          <Textarea
            id="support_channels"
            {...register('details.support_channels_text')}
            placeholder="Email, Live chat, Phone, Documentation..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter support channels separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="target_audience">Target Audience (comma-separated)</Label>
          <Textarea
            id="target_audience"
            {...register('details.target_audience_text')}
            placeholder="Developers, Data scientists, Business analysts..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter target audiences separated by commas.
          </p>
        </div>

        <div>
          <Label htmlFor="use_cases">Use Cases (comma-separated)</Label>
          <Textarea
            id="use_cases"
            {...register('details.use_cases_text')}
            placeholder="Data analysis, Web development, Machine learning..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter use cases separated by commas.
          </p>
        </div>
      </div>
    </div>
  );
}
