// Comprehensive E2E test for all filter functionality
const API_BASE_URL = 'http://localhost:3000';

// Test data for all filter types
const testFilters = {
  // Basic filters
  basic: {
    searchTerm: 'AI',
    page: 1,
    limit: 5,
    sortBy: 'averageRating',
    sortOrder: 'desc'
  },
  
  // Rating and review filters
  quality: {
    rating_min: 3,
    rating_max: 5,
    review_count_min: 5,
    review_count_max: 100
  },
  
  // Affiliate filters
  affiliate: {
    affiliate_status: 'APPROVED',
    has_affiliate_link: true
  },
  
  // Platform and integration filters
  platform: {
    platforms: ['Web', 'iOS', 'Android'],
    integrations: ['GitHub', 'Slack', 'Zapier'],
    targetAudience: ['Developers', 'Designers']
  },
  
  // Status filters
  status: {
    status: 'ACTIVE'
  },
  
  // Business filters
  business: {
    hasFreeTier: true,
    pricingModels: ['FREE', 'FREEMIUM'],
    priceRanges: ['FREE', 'LOW'],
    employeeCountRanges: ['C1_10', 'C11_50'],
    fundingStages: ['SEED', 'SERIES_A']
  },
  
  // Technical filters
  technical: {
    apiAccess: true,
    createdAtFrom: '2023-01-01',
    createdAtTo: '2024-12-31'
  },
  
  // Entity-specific filters
  entitySpecific: {
    entity_type_filters: JSON.stringify({
      Course: {
        skill_levels: ['BEGINNER', 'INTERMEDIATE'],
        certificate_available: true,
        duration_text: '10 hours'
      },
      Job: {
        employment_types: ['Full-time', 'Remote'],
        experience_levels: ['Mid', 'Senior'],
        salary_min: 80,
        salary_max: 150
      },
      Hardware: {
        hardware_types: ['GPU', 'CPU'],
        manufacturers: ['NVIDIA', 'Intel'],
        price_min: 500,
        price_max: 2000
      },
      Event: {
        event_types: ['Conference', 'Workshop'],
        is_online: true,
        registration_required: true
      }
    })
  }
};

async function testFilterCategory(categoryName, filters, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`📂 Category: ${categoryName}`);
    
    const queryString = new URLSearchParams();
    
    // Add filters to query string
    Object.entries(filters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => queryString.append(key, v));
      } else if (value !== null && value !== undefined) {
        queryString.append(key, value.toString());
      }
    });
    
    const url = `${API_BASE_URL}/entities?${queryString.toString()}`;
    console.log(`📡 URL: ${url.substring(0, 100)}${url.length > 100 ? '...' : ''}`);
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Results: ${data.data?.length || 0} entities found`);
      console.log(`📄 Total: ${data.meta?.totalItems || 0} total items`);
      
      // Log active filters
      const activeFilters = Object.keys(filters).length;
      console.log(`🔧 Active filters: ${activeFilters}`);
      
      return { success: true, count: data.data?.length || 0, total: data.meta?.totalItems || 0 };
    } else {
      console.log(`❌ Error: ${response.status} - ${data.message || 'Unknown error'}`);
      return { success: false, error: data.message };
    }
  } catch (error) {
    console.log(`💥 Request failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testFilterCombinations() {
  console.log('🔄 Testing filter combinations...\n');
  
  // Test combining multiple filter categories
  const combinations = [
    {
      name: 'Quality + Platform',
      filters: { ...testFilters.quality, ...testFilters.platform }
    },
    {
      name: 'Business + Technical',
      filters: { ...testFilters.business, ...testFilters.technical }
    },
    {
      name: 'All Basic Filters',
      filters: { 
        ...testFilters.basic, 
        ...testFilters.quality, 
        ...testFilters.affiliate,
        ...testFilters.status
      }
    }
  ];
  
  for (const combo of combinations) {
    await testFilterCategory('combination', combo.filters, `Combined filters: ${combo.name}`);
  }
}

async function testEntitySpecificFilters() {
  console.log('\n🎯 Testing entity-specific filters...\n');
  
  // Test each entity type separately
  const entityTypes = ['Course', 'Job', 'Hardware', 'Event'];
  
  for (const entityType of entityTypes) {
    const entityFilters = JSON.parse(testFilters.entitySpecific.entity_type_filters);
    const singleEntityFilter = {
      entity_type_filters: JSON.stringify({
        [entityType]: entityFilters[entityType]
      })
    };
    
    await testFilterCategory(
      'entity-specific', 
      singleEntityFilter, 
      `${entityType}-specific filters`
    );
  }
}

async function runComprehensiveTests() {
  console.log('🚀 Starting comprehensive filter testing...\n');
  console.log('=' * 60);
  
  const results = [];
  
  // Test each filter category
  for (const [categoryName, filters] of Object.entries(testFilters)) {
    if (categoryName === 'entitySpecific') continue; // Handle separately
    
    const result = await testFilterCategory(
      categoryName, 
      filters, 
      `${categoryName.charAt(0).toUpperCase() + categoryName.slice(1)} filters`
    );
    results.push({ category: categoryName, ...result });
  }
  
  // Test filter combinations
  await testFilterCombinations();
  
  // Test entity-specific filters
  await testEntitySpecificFilters();
  
  // Summary
  console.log('\n' + '=' * 60);
  console.log('📊 TEST SUMMARY');
  console.log('=' * 60);
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Successful tests: ${successful}`);
  console.log(`❌ Failed tests: ${failed}`);
  console.log(`📈 Success rate: ${((successful / results.length) * 100).toFixed(1)}%`);
  
  if (failed > 0) {
    console.log('\n❌ Failed categories:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.category}: ${r.error}`);
    });
  }
  
  console.log('\n🎉 Comprehensive filter testing completed!');
}

// Frontend URL tests
function testFrontendUrls() {
  console.log('\n🌐 Frontend URL tests:');
  
  const frontendTests = [
    'http://localhost:3001/browse',
    'http://localhost:3001/browse?rating_min=4&rating_max=5',
    'http://localhost:3001/browse?platforms=Web,iOS&integrations=GitHub',
    'http://localhost:3001/browse?status=ACTIVE&hasFreeTier=true',
    'http://localhost:3001/browse?sortBy=averageRating&sortOrder=desc',
    'http://localhost:3001/test-filters-debug'
  ];
  
  frontendTests.forEach((url, index) => {
    console.log(`${index + 1}. ${url}`);
  });
  
  console.log('\n💡 Open these URLs in your browser to test frontend functionality');
}

// Run all tests
runComprehensiveTests()
  .then(() => {
    testFrontendUrls();
  })
  .catch(console.error);
